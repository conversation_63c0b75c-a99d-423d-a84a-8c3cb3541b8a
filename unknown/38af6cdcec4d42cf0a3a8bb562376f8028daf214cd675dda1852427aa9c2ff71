import 'package:drift/drift.dart';
import '../../domain/entities/transaction.dart';
import '../../../../core/datasources/local/database/database.dart';

extension TransactionEntityMapper on TransactionEntity {
  TransactionsCompanion toCompanion() {
    return TransactionsCompanion(
      id: Value(id),
      date: Value(date),
      totalAmount: Value(totalAmount),
      remainingAmount: Value(remainingAmount),
      status: Value(status),
      createdAt: Value(createdAt),
      updatedAt: Value(DateTime.now()),
    );
  }
}

extension TransactionMapper on Transaction {
  TransactionEntity toEntity() {
    return TransactionEntity(
      id: id,
      date: date,
      totalAmount: totalAmount,
      remainingAmount: remainingAmount,
      status: status,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
